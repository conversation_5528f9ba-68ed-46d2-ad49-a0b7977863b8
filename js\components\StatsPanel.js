/**
 * 统计面板组件
 * 负责统计数据计算和显示
 */
class StatsPanel {
  constructor(eventManager) {
    this.eventManager = eventManager;

    // DOM元素引用
    this.elements = {
      totalItems: DOMUtils.$(AppConstants.SELECTORS.TOTAL_ITEMS),
      completedItems: DOMUtils.$(AppConstants.SELECTORS.COMPLETED_ITEMS),
      totalTime: DOMUtils.$(AppConstants.SELECTORS.TOTAL_TIME),
      progressPercent: DOMUtils.$(AppConstants.SELECTORS.PROGRESS_PERCENT),
      progressFill: DOMUtils.$(AppConstants.SELECTORS.PROGRESS_FILL),
      resetProgressBtn: DOMUtils.$(AppConstants.SELECTORS.RESET_PROGRESS_BTN)
    };

    // 当前统计数据
    this.currentStats = {
      totalItems: 0,
      completedItems: 0,
      totalTime: 0,
      progressPercent: 0,
      formattedTime: '0s'
    };

    this.init();
  }

  /**
   * 初始化统计面板组件
   */
  init() {
    this.bindEvents();
    this.updateDisplay();
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 监听统计更新事件
    console.log('[StatsPanel] 注册STATS_UPDATED事件监听器');
    this.eventManager.on(AppConstants.EVENTS.STATS_UPDATED, (data) => {
      console.log('[StatsPanel] 收到STATS_UPDATED事件:', data);
      this.updateStats(data.stats);
    });

    // 监听进度更新事件
    this.eventManager.on(AppConstants.EVENTS.PROGRESS_UPDATED, (data) => {
      this.updateProgress(data.progress);
    });

    // 监听数据清空事件
    this.eventManager.on(AppConstants.EVENTS.DATA_CLEARED, () => {
      this.resetStats();
    });

    // 绑定重置进度按钮事件
    if (this.elements.resetProgressBtn) {
      DOMUtils.on(this.elements.resetProgressBtn, 'click', () => {
        this.handleResetProgress();
      });
    }
  }

  /**
   * 更新统计数据
   * @param {Object} stats - 统计数据对象
   */
  updateStats(stats) {
    // {{CHENGQI:
    // Action: Added
    // Timestamp: [2025-01-28 00:50:00 +08:00]
    // Reason: 添加调试日志验证统计数据更新过程 - 任务4.2
    // Principle_Applied: 可维护性 - 添加详细日志便于问题排查和调试
    // Optimization: 帮助诊断状态同步问题，验证数据流程正确性
    // Documentation_Note (DW): 调试日志用于验证问题4状态同步的修复效果
    // }}
    console.log('[StatsPanel] 接收到统计更新:', stats);
    console.log('[StatsPanel] DOM元素状态检查:', {
      totalItems: !!this.elements.totalItems,
      completedItems: !!this.elements.completedItems,
      totalTime: !!this.elements.totalTime,
      progressPercent: !!this.elements.progressPercent,
      progressFill: !!this.elements.progressFill
    });

    if (!stats || typeof stats !== 'object') {
      console.warn('[StatsPanel] 无效的统计数据:', stats);
      return;
    }

    // 更新内部数据
    this.currentStats = {
      totalItems: stats.totalItems || 0,
      completedItems: stats.completedItems || 0,
      totalTime: stats.totalTime || 0,
      progressPercent: stats.progressPercent || 0,
      formattedTime: stats.formattedTime || TimeUtils.formatTime(stats.totalTime || 0, false)
    };

    console.log('[StatsPanel] 内部数据已更新:', this.currentStats);

    // 更新显示
    this.updateDisplay();

    // 添加动画效果
    this.animateUpdate();
  }

  /**
   * 更新进度百分比
   * @param {number} progress - 进度百分比
   */
  updateProgress(progress) {
    console.log('[StatsPanel] 收到进度更新:', progress);
    if (typeof progress === 'number' && progress >= 0 && progress <= 100) {
      this.currentStats.progressPercent = progress;
      console.log('[StatsPanel] 进度已更新，调用updateProgressDisplay');
      this.updateProgressDisplay();
    } else {
      console.warn('[StatsPanel] 无效的进度值:', progress);
    }
  }

  /**
   * 更新显示内容
   */
  updateDisplay() {
    // {{CHENGQI:
    // Action: Added
    // Timestamp: [2025-01-28 00:52:00 +08:00]
    // Reason: 添加DOM验证日志，确保元素引用有效 - 任务4.2
    // Principle_Applied: 可维护性 - 详细日志便于诊断DOM元素引用问题
    // Optimization: 帮助验证状态同步问题的修复效果
    // }}
    console.log('[StatsPanel] 开始更新显示，当前数据:', this.currentStats);

    // 验证关键DOM元素是否存在
    const domValidation = {
      totalItems: !!this.elements.totalItems,
      completedItems: !!this.elements.completedItems,
      totalTime: !!this.elements.totalTime,
      progressPercent: !!this.elements.progressPercent,
      progressFill: !!this.elements.progressFill
    };
    console.log('[StatsPanel] DOM元素验证结果:', domValidation);

    // 更新总项目数
    if (this.elements.totalItems) {
      DOMUtils.setContent(this.elements.totalItems, this.currentStats.totalItems.toString());
      console.log('[StatsPanel] 总项目数已更新:', this.currentStats.totalItems);
    } else {
      console.warn('[StatsPanel] 总项目数元素不存在');
    }

    // 更新已完成项目数
    if (this.elements.completedItems) {
      DOMUtils.setContent(this.elements.completedItems, this.currentStats.completedItems.toString());
      console.log('[StatsPanel] 已完成项目数已更新:', this.currentStats.completedItems);
    } else {
      console.warn('[StatsPanel] 已完成项目数元素不存在');
    }

    // 更新总学习时间
    if (this.elements.totalTime) {
      DOMUtils.setContent(this.elements.totalTime, this.currentStats.formattedTime);
      console.log('[StatsPanel] 总学习时间已更新:', this.currentStats.formattedTime);
    } else {
      console.warn('[StatsPanel] 总学习时间元素不存在');
    }

    // 更新进度百分比
    this.updateProgressDisplay();
    console.log('[StatsPanel] 显示更新完成');
  }

  /**
   * 更新进度显示
   */
  updateProgressDisplay() {
    // 更新进度百分比文本
    if (this.elements.progressPercent) {
      DOMUtils.setContent(this.elements.progressPercent, `${this.currentStats.progressPercent}%`);
    }

    // 更新进度条填充
    if (this.elements.progressFill) {
      DOMUtils.setStyle(this.elements.progressFill, 'width', `${this.currentStats.progressPercent}%`);

      // 根据进度添加不同的颜色类
      this.updateProgressColor();
    }
  }

  /**
   * 根据进度更新进度条颜色
   */
  updateProgressColor() {
    if (!this.elements.progressFill) return;

    const progress = this.currentStats.progressPercent;

    // 移除之前的颜色类
    DOMUtils.removeClass(this.elements.progressFill, ['progress-low', 'progress-medium', 'progress-high', 'progress-complete']);

    // 根据进度添加相应的颜色类
    if (progress === 100) {
      DOMUtils.addClass(this.elements.progressFill, 'progress-complete');
    } else if (progress >= 75) {
      DOMUtils.addClass(this.elements.progressFill, 'progress-high');
    } else if (progress >= 50) {
      DOMUtils.addClass(this.elements.progressFill, 'progress-medium');
    } else if (progress > 0) {
      DOMUtils.addClass(this.elements.progressFill, 'progress-low');
    }
  }

  /**
   * 添加更新动画效果
   */
  animateUpdate() {
    // 为统计卡片添加更新动画
    const statCards = [
      this.elements.totalItems?.closest('.stat-card'),
      this.elements.completedItems?.closest('.stat-card'),
      this.elements.totalTime?.closest('.stat-card'),
      this.elements.progressPercent?.closest('.stat-card')
    ].filter(Boolean);

    statCards.forEach(card => {
      if (card) {
        DOMUtils.addClass(card, 'stat-updated');

        // 移除动画类
        setTimeout(() => {
          DOMUtils.removeClass(card, 'stat-updated');
        }, 600);
      }
    });
  }

  /**
   * 重置统计数据
   */
  resetStats() {
    this.currentStats = {
      totalItems: 0,
      completedItems: 0,
      totalTime: 0,
      progressPercent: 0,
      formattedTime: '0s'
    };

    this.updateDisplay();
  }

  /**
   * 处理重置进度按钮点击
   */
  handleResetProgress() {
    if (confirm(AppConstants.CONFIRM_MESSAGES.RESET_PROGRESS)) {
      // 触发重置进度事件，由DataManager处理
      this.eventManager.emit('progress:reset');

      // 显示成功消息
      this.showSuccessMessage(AppConstants.SUCCESS_MESSAGES.PROGRESS_RESET);
    }
  }

  /**
   * 显示成功消息
   * @param {string} message - 消息内容
   */
  showSuccessMessage(message) {
    // 创建临时提示元素
    const notification = DOMUtils.createElement('div', {
      className: 'success-notification',
      textContent: message
    });

    // 添加到页面
    document.body.appendChild(notification);

    // 显示动画
    setTimeout(() => {
      DOMUtils.addClass(notification, 'show');
    }, 10);

    // 自动移除
    setTimeout(() => {
      DOMUtils.removeClass(notification, 'show');
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 2000);
  }

  /**
   * 获取当前统计数据
   * @returns {Object} 当前统计数据
   */
  getCurrentStats() {
    return { ...this.currentStats };
  }

  /**
   * 格式化统计数据用于显示
   * @param {Object} stats - 原始统计数据
   * @returns {Object} 格式化后的统计数据
   */
  formatStatsForDisplay(stats) {
    return {
      totalItems: stats.totalItems || 0,
      completedItems: stats.completedItems || 0,
      totalTime: TimeUtils.formatTime(stats.totalTime || 0, false),
      progressPercent: Math.round(stats.progressPercent || 0),
      completionRate: stats.totalItems > 0 ?
        Math.round((stats.completedItems / stats.totalItems) * 100) : 0
    };
  }

  /**
   * 获取进度状态描述
   * @returns {string} 进度状态描述
   */
  getProgressStatus() {
    const progress = this.currentStats.progressPercent;

    if (progress === 100) {
      return '🎉 恭喜完成！';
    } else if (progress >= 75) {
      return '🚀 即将完成';
    } else if (progress >= 50) {
      return '💪 进展良好';
    } else if (progress >= 25) {
      return '📈 稳步前进';
    } else if (progress > 0) {
      return '🌱 刚刚开始';
    } else {
      return '📋 等待开始';
    }
  }

  /**
   * 导出统计数据
   * @returns {Object} 导出的统计数据
   */
  exportStats() {
    return {
      ...this.currentStats,
      exportTime: Date.now(),
      progressStatus: this.getProgressStatus(),
      formattedStats: this.formatStatsForDisplay(this.currentStats)
    };
  }

  /**
   * 检查是否有学习活动
   * @returns {boolean} 是否有学习活动
   */
  hasLearningActivity() {
    return this.currentStats.totalItems > 0 || this.currentStats.totalTime > 0;
  }

  /**
   * 获取学习效率统计
   * @returns {Object} 学习效率统计
   */
  getEfficiencyStats() {
    const { totalItems, completedItems, totalTime } = this.currentStats;

    return {
      averageTimePerItem: totalItems > 0 ? Math.round(totalTime / totalItems) : 0,
      averageTimePerCompletedItem: completedItems > 0 ? Math.round(totalTime / completedItems) : 0,
      completionRate: totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0,
      formattedAverageTime: totalItems > 0 ?
        TimeUtils.formatTime(Math.round(totalTime / totalItems)) : '0s'
    };
  }

  /**
   * 销毁组件
   */
  destroy() {
    // 移除事件监听器
    this.eventManager.removeAllListeners(AppConstants.EVENTS.STATS_UPDATED);
    this.eventManager.removeAllListeners(AppConstants.EVENTS.PROGRESS_UPDATED);
    this.eventManager.removeAllListeners(AppConstants.EVENTS.DATA_CLEARED);

    // 清理数据
    this.currentStats = null;
    this.elements = {};
  }
}

// 导出组件
window.StatsPanel = StatsPanel;

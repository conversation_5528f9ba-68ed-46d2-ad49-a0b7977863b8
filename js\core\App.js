/**
 * 主应用管理器
 * 负责模块初始化、协调、生命周期管理
 */
class LearningRoadmapApp {
  constructor() {
    // 核心系统
    this.eventManager = null;

    // 业务模块
    this.fileHandler = null;
    this.markdownParser = null;
    this.dataManager = null;
    this.renderEngine = null;

    // UI组件
    this.statsPanel = null;
    this.timeTracker = null;
    this.themeManager = null;
    this.searchComponent = null;

    // 应用状态
    this.isInitialized = false;
    this.isDestroyed = false;

    // 绑定方法上下文
    this.init = this.init.bind(this);
    this.destroy = this.destroy.bind(this);
    this.handleError = this.handleError.bind(this);
  }

  /**
   * 初始化应用
   */
  async init() {
    try {
      console.log('🚀 开始初始化学习路线管理系统...');

      // 检查依赖
      this.checkDependencies();

      // 初始化核心系统
      await this.initCore();

      // 初始化业务模块
      await this.initModules();

      // 初始化UI组件
      await this.initComponents();

      // 绑定全局事件
      this.bindGlobalEvents();

      // 标记为已初始化
      this.isInitialized = true;

      // 触发应用就绪事件
      this.eventManager.emit(AppConstants.EVENTS.APP_READY, {
        timestamp: Date.now(),
        version: '2.0.0'
      });

      console.log('✅ 学习路线管理系统初始化完成');

    } catch (error) {
      console.error('❌ 应用初始化失败:', error);
      this.handleError(error);
      throw error;
    }
  }

  /**
   * 检查依赖
   */
  checkDependencies() {
    const requiredClasses = [
      'EventManager', 'FileHandler', 'MarkdownParser', 'DataManager',
      'RenderEngine', 'StatsPanel', 'TimeTracker', 'ThemeManager',
      'SearchComponent', 'DOMUtils', 'TimeUtils', 'StorageUtils'
    ];

    const missing = requiredClasses.filter(className => !window[className]);

    if (missing.length > 0) {
      throw new Error(`缺少必需的依赖: ${missing.join(', ')}`);
    }

    if (!window.AppConstants) {
      throw new Error('缺少应用常量配置 (AppConstants)');
    }

    console.log('✅ 依赖检查通过');
  }

  /**
   * 初始化核心系统
   */
  async initCore() {
    console.log('🔧 初始化核心系统...');

    // 创建事件管理器
    this.eventManager = new EventManager();
    this.eventManager.setDebug(true); // {{CHENGQI: 临时启用调试模式，用于诊断状态同步问题}}

    // 触发应用初始化事件
    this.eventManager.emit(AppConstants.EVENTS.APP_INIT, {
      timestamp: Date.now()
    });

    console.log('✅ 核心系统初始化完成');
  }

  /**
   * 初始化业务模块
   */
  async initModules() {
    console.log('📦 初始化业务模块...');

    try {
      // 按依赖顺序初始化模块
      this.dataManager = new DataManager(this.eventManager);
      this.markdownParser = new MarkdownParser(this.eventManager);
      this.fileHandler = new FileHandler(this.eventManager);
      this.renderEngine = new RenderEngine(this.eventManager);

      console.log('✅ 业务模块初始化完成');

    } catch (error) {
      console.error('❌ 业务模块初始化失败:', error);
      throw error;
    }
  }

  /**
   * 初始化UI组件
   */
  async initComponents() {
    console.log('🎨 初始化UI组件...');

    try {
      // 初始化UI组件
      this.statsPanel = new StatsPanel(this.eventManager);
      this.timeTracker = new TimeTracker(this.eventManager);
      this.themeManager = new ThemeManager(this.eventManager);
      this.searchComponent = new SearchComponent(this.eventManager);

      console.log('✅ UI组件初始化完成');

    } catch (error) {
      console.error('❌ UI组件初始化失败:', error);
      throw error;
    }
  }

  /**
   * 绑定全局事件
   */
  bindGlobalEvents() {
    console.log('🔗 绑定全局事件...');

    // 监听应用级别事件
    this.eventManager.on('progress:reset', () => {
      if (this.dataManager) {
        this.dataManager.resetAllProgress();
      }
    });

    this.eventManager.on('items:expand_all', () => {
      if (this.dataManager) {
        this.dataManager.setAllItemsCollapsed(false);
        this.eventManager.emit(AppConstants.EVENTS.ROADMAP_UPDATED, {
          roadmapData: this.dataManager.getRoadmapData()
        });
      }
    });

    this.eventManager.on('items:collapse_all', () => {
      if (this.dataManager) {
        this.dataManager.setAllItemsCollapsed(true);
        this.eventManager.emit(AppConstants.EVENTS.ROADMAP_UPDATED, {
          roadmapData: this.dataManager.getRoadmapData()
        });
      }
    });

    this.eventManager.on('notes:save', (data) => {
      if (this.dataManager) {
        this.dataManager.updateItemNotes(data.itemId, data.notes);
      }
    });

    // 监听错误事件
    this.eventManager.on(AppConstants.EVENTS.FILE_UPLOAD_ERROR, (data) => {
      this.handleError(new Error(data.message));
    });

    // 窗口关闭前的清理
    window.addEventListener('beforeunload', () => {
      this.destroy();
    });

    // 全局错误处理
    window.addEventListener('error', (event) => {
      this.handleError(event.error);
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(event.reason);
    });

    console.log('✅ 全局事件绑定完成');
  }

  /**
   * 错误处理
   */
  handleError(error) {
    console.error('应用错误:', error);

    // 可以在这里添加错误上报逻辑
    // 或者显示用户友好的错误消息

    if (this.eventManager) {
      this.eventManager.emit('app:error', {
        error: error,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 获取应用状态
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isDestroyed: this.isDestroyed,
      modules: {
        eventManager: !!this.eventManager,
        fileHandler: !!this.fileHandler,
        markdownParser: !!this.markdownParser,
        dataManager: !!this.dataManager,
        renderEngine: !!this.renderEngine,
        statsPanel: !!this.statsPanel,
        timeTracker: !!this.timeTracker,
        themeManager: !!this.themeManager,
        searchComponent: !!this.searchComponent
      }
    };
  }

  /**
   * 清空搜索（供全局调用）
   */
  clearSearch() {
    if (this.searchComponent) {
      this.searchComponent.clearSearch();
    }
  }

  /**
   * 获取当前主题（供全局调用）
   */
  getCurrentTheme() {
    return this.themeManager ? this.themeManager.getCurrentTheme() : 'light';
  }

  /**
   * 切换主题（供全局调用）
   */
  toggleTheme() {
    if (this.themeManager) {
      this.themeManager.toggleTheme();
    }
  }

  /**
   * 获取统计信息（供全局调用）
   */
  getStats() {
    return this.statsPanel ? this.statsPanel.getCurrentStats() : null;
  }

  /**
   * 销毁应用
   */
  destroy() {
    if (this.isDestroyed) {
      return;
    }

    console.log('🔄 正在销毁应用...');

    try {
      // 触发销毁事件
      if (this.eventManager) {
        this.eventManager.emit(AppConstants.EVENTS.APP_DESTROY, {
          timestamp: Date.now()
        });
      }

      // 销毁UI组件
      if (this.searchComponent) {
        this.searchComponent.destroy();
        this.searchComponent = null;
      }

      if (this.themeManager) {
        this.themeManager.destroy();
        this.themeManager = null;
      }

      if (this.timeTracker) {
        this.timeTracker.destroy();
        this.timeTracker = null;
      }

      if (this.statsPanel) {
        this.statsPanel.destroy();
        this.statsPanel = null;
      }

      // 销毁业务模块
      if (this.renderEngine) {
        this.renderEngine.destroy();
        this.renderEngine = null;
      }

      if (this.fileHandler) {
        this.fileHandler.destroy();
        this.fileHandler = null;
      }

      if (this.markdownParser) {
        this.markdownParser.destroy();
        this.markdownParser = null;
      }

      if (this.dataManager) {
        this.dataManager.destroy();
        this.dataManager = null;
      }

      // 最后销毁事件管理器
      if (this.eventManager) {
        this.eventManager.destroy();
        this.eventManager = null;
      }

      // 标记为已销毁
      this.isDestroyed = true;
      this.isInitialized = false;

      console.log('✅ 应用销毁完成');

    } catch (error) {
      console.error('❌ 应用销毁过程中出现错误:', error);
    }
  }
}

// 全局应用实例
window.app = null;

// 应用启动函数
window.startApp = async function() {
  try {
    if (window.app) {
      console.warn('应用已经启动');
      return window.app;
    }

    window.app = new LearningRoadmapApp();
    await window.app.init();

    return window.app;

  } catch (error) {
    console.error('应用启动失败:', error);
    throw error;
  }
};

// DOM加载完成后自动启动应用
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', window.startApp);
} else {
  // 如果DOM已经加载完成，立即启动
  window.startApp();
}

// 导出应用类
window.LearningRoadmapApp = LearningRoadmapApp;
